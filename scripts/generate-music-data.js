/**
 * 生成50条音乐数据的脚本
 * 运行：node scripts/generate-music-data.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 音乐数据模板
const musicTemplates = [
  {
    title: '夜空中最亮的星',
    labelName: '星光唱片',
    albumName: '星空物语',
    trackInfo: '一首关于梦想和希望的歌曲',
    primaryGenreId: 'pop',
    secondaryGenreId: 'folk',
  },
  {
    title: '青春的回忆',
    labelName: '青春唱片',
    albumName: '时光倒流',
    trackInfo: '回忆青春岁月的温暖歌曲',
    primaryGenreId: 'pop',
    secondaryGenreId: 'rock',
  },
  {
    title: '城市的灯火',
    labelName: '都市音乐',
    albumName: '霓虹夜色',
    trackInfo: '描绘城市夜景的现代歌曲',
    primaryGenreId: 'electronic',
    secondaryGenreId: 'pop',
  },
  {
    title: '海边的风',
    labelName: '海洋唱片',
    albumName: '蓝色心情',
    trackInfo: '清新的海边风情歌曲',
    primaryGenreId: 'folk',
    secondaryGenreId: 'acoustic',
  },
  {
    title: '雨后彩虹',
    labelName: '彩虹音乐',
    albumName: '希望之光',
    trackInfo: '充满希望和正能量的歌曲',
    primaryGenreId: 'pop',
    secondaryGenreId: 'inspirational',
  },
];

// 生成50条音乐数据
function generateMusicData() {
  const musicData = [];
  const coverUrls = [];

  // 生成10个封面URL
  for (let i = 1; i <= 10; i++) {
    coverUrls.push(`https://example.com/cover${i}.jpg`);
  }

  // 生成50条音乐数据
  for (let i = 1; i <= 50; i++) {
    const template = musicTemplates[(i - 1) % musicTemplates.length];
    const trackNumber = String(i).padStart(3, '0');

    const track = {
      title: `${template.title} ${i}`,
      labelName: template.labelName,
      albumName: `${template.albumName} Vol.${Math.ceil(i / 10)}`,
      trackInfo: `${template.trackInfo} - 第${i}首`,
      primaryLanguage: 'zh',
      upc: `12345678${trackNumber}`,
      isrc: `CNRC240${trackNumber}`,
      primaryGenreId: template.primaryGenreId,
      secondaryGenreId: template.secondaryGenreId,
      copyrightName: `${template.labelName}版权公司`,
      copyrightYear: 2024,
      phonogramCopyright: `${template.labelName}录音版权`,
      phonogramCopyrightYear: 2024,
      audioFormats: ['mp3', 'wav'],
      releaseOptions: ['digital', 'streaming'],
      mediaUrls: [`https://example.com/audio/track${trackNumber}.mp3`],
    };

    musicData.push(track);
  }

  return {
    coverUrls,
    musicData,
  };
}

// 主函数
function main() {
  console.log('🎵 生成50条音乐数据...');

  const data = generateMusicData();
  const outputFile = path.join(__dirname, 'music-data-50-tracks.json');

  // 写入文件
  fs.writeFileSync(outputFile, JSON.stringify(data, null, 2), 'utf8');

  console.log(`✅ 成功生成 ${data.musicData.length} 条音乐数据`);
  console.log(`📄 文件保存到: ${outputFile}`);
  console.log(`🖼️  包含 ${data.coverUrls.length} 个封面URL`);

  // 显示前5条数据作为示例
  console.log('\n📋 前5条数据示例:');
  data.musicData.slice(0, 5).forEach((track, index) => {
    console.log(`${index + 1}. ${track.title} - ${track.albumName}`);
  });
}

// 运行
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { generateMusicData };
