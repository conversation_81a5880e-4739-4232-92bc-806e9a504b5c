/**
 * 生成50首真实英文歌曲数据的脚本
 * 运行：node scripts/generate-english-songs.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 50首流行英文歌曲数据
const englishSongs = [
  {
    title: "Blinding Lights",
    labelName: "Starlight Records",
    albumName: "Neon Dreams",
    trackInfo: "An electrifying synthwave anthem about love and longing in the city lights",
    primaryGenreId: "pop",
    secondaryGenreId: "electronic"
  },
  {
    title: "Watermelon Sugar",
    labelName: "Summer Vibes Records", 
    albumName: "Sweet Memories",
    trackInfo: "A feel-good summer anthem celebrating life's simple pleasures and sweet moments",
    primaryGenreId: "pop",
    secondaryGenreId: "rock"
  },
  {
    title: "Levitating",
    labelName: "Future Pop Records",
    albumName: "Disco Fever", 
    trackInfo: "An infectious disco-pop track that makes you want to dance all night long",
    primaryGenreId: "pop",
    secondaryGenreId: "dance"
  },
  {
    title: "Good 4 U",
    labelName: "Indie Rock Records",
    albumName: "Emotional Rollercoaster",
    trackInfo: "A powerful pop-punk anthem about moving on and finding strength after heartbreak",
    primaryGenreId: "rock",
    secondaryGenreId: "pop"
  },
  {
    title: "Stay",
    labelName: "Heartfelt Music",
    albumName: "Vulnerable Moments",
    trackInfo: "An emotional ballad about the fear of losing someone you love and wanting them to stay",
    primaryGenreId: "pop",
    secondaryGenreId: "ballad"
  },
  {
    title: "Anti-Hero",
    labelName: "Midnight Records",
    albumName: "Self Reflection",
    trackInfo: "A introspective pop song about self-doubt and the complexities of being human",
    primaryGenreId: "pop",
    secondaryGenreId: "alternative"
  },
  {
    title: "As It Was",
    labelName: "Nostalgic Sounds",
    albumName: "Time Capsule",
    trackInfo: "A melancholic reflection on change and the passage of time with dreamy melodies",
    primaryGenreId: "pop",
    secondaryGenreId: "indie"
  },
  {
    title: "Heat Waves",
    labelName: "Indie Wave Records",
    albumName: "Summer Haze",
    trackInfo: "A dreamy indie-pop track about longing and the hazy memories of summer love",
    primaryGenreId: "indie",
    secondaryGenreId: "pop"
  },
  {
    title: "Bad Habit",
    labelName: "Smooth Groove Records",
    albumName: "Midnight Confessions",
    trackInfo: "A smooth R&B-influenced track about irresistible attraction and late-night desires",
    primaryGenreId: "rnb",
    secondaryGenreId: "pop"
  },
  {
    title: "About Damn Time",
    labelName: "Empowerment Records",
    albumName: "Self Love Anthem",
    trackInfo: "An uplifting anthem about self-confidence and celebrating your own worth",
    primaryGenreId: "pop",
    secondaryGenreId: "funk"
  },
  {
    title: "Running Up That Hill",
    labelName: "Classic Revival Records",
    albumName: "Timeless Echoes",
    trackInfo: "A haunting synth-pop masterpiece about empathy and understanding between lovers",
    primaryGenreId: "pop",
    secondaryGenreId: "synthpop"
  },
  {
    title: "Flowers",
    labelName: "Self Love Records",
    albumName: "Independence Day",
    trackInfo: "An empowering pop anthem about self-love and finding strength after a breakup",
    primaryGenreId: "pop",
    secondaryGenreId: "dance"
  },
  {
    title: "Unholy",
    labelName: "Dark Pop Records",
    albumName: "Forbidden Desires",
    trackInfo: "A seductive dark pop track exploring themes of temptation and forbidden love",
    primaryGenreId: "pop",
    secondaryGenreId: "dark"
  },
  {
    title: "I'm Good (Blue)",
    labelName: "Electronic Beats",
    albumName: "Dance Floor Anthems",
    trackInfo: "An energetic dance track that combines classic house vibes with modern production",
    primaryGenreId: "electronic",
    secondaryGenreId: "dance"
  },
  {
    title: "Calm Down",
    labelName: "Afrobeats Global",
    albumName: "African Rhythms",
    trackInfo: "A smooth Afrobeats track with infectious rhythms and romantic lyrics",
    primaryGenreId: "afrobeats",
    secondaryGenreId: "pop"
  },
  {
    title: "Shivers",
    labelName: "Acoustic Soul Records",
    albumName: "Intimate Moments",
    trackInfo: "A tender acoustic ballad about the physical and emotional effects of deep love",
    primaryGenreId: "pop",
    secondaryGenreId: "acoustic"
  },
  {
    title: "Industry Baby",
    labelName: "Hip Hop Empire",
    albumName: "Chart Toppers",
    trackInfo: "A bold hip-hop anthem celebrating success and breaking barriers in the music industry",
    primaryGenreId: "hiphop",
    secondaryGenreId: "rap"
  },
  {
    title: "Ghost",
    labelName: "Indie Folk Records",
    albumName: "Haunted Melodies",
    trackInfo: "A haunting folk ballad about lost love and the memories that linger like ghosts",
    primaryGenreId: "folk",
    secondaryGenreId: "indie"
  },
  {
    title: "Peaches",
    labelName: "R&B Smooth",
    albumName: "Velvet Nights",
    trackInfo: "A smooth R&B track with sensual lyrics and silky vocals about desire and intimacy",
    primaryGenreId: "rnb",
    secondaryGenreId: "pop"
  },
  {
    title: "Montero (Call Me By Your Name)",
    labelName: "Bold Expression Records",
    albumName: "Authentic Self",
    trackInfo: "A provocative pop-rap anthem about self-acceptance and living authentically",
    primaryGenreId: "pop",
    secondaryGenreId: "rap"
  },
  {
    title: "Drivers License",
    labelName: "Teenage Dreams Records",
    albumName: "Coming of Age",
    trackInfo: "A heartbreaking ballad about first love, heartbreak, and growing up too fast",
    primaryGenreId: "pop",
    secondaryGenreId: "ballad"
  },
  {
    title: "Positions",
    labelName: "Sultry Sounds",
    albumName: "Intimate Confessions",
    trackInfo: "A sultry R&B-pop track about devotion and the many ways to show love",
    primaryGenreId: "pop",
    secondaryGenreId: "rnb"
  },
  {
    title: "Willow",
    labelName: "Folk Pop Records",
    albumName: "Nature's Symphony",
    trackInfo: "A dreamy folk-pop song using nature metaphors to describe the flow of love",
    primaryGenreId: "folk",
    secondaryGenreId: "pop"
  },
  {
    title: "Cardigan",
    labelName: "Storyteller Records",
    albumName: "Folklore Tales",
    trackInfo: "A nostalgic indie-folk ballad about young love and the comfort of familiar memories",
    primaryGenreId: "folk",
    secondaryGenreId: "indie"
  },
  {
    title: "Circles",
    labelName: "Melancholy Music",
    albumName: "Endless Loops",
    trackInfo: "A melancholic hip-hop ballad about being stuck in toxic relationship patterns",
    primaryGenreId: "hiphop",
    secondaryGenreId: "rnb"
  },
  {
    title: "Savage",
    labelName: "Confidence Records",
    albumName: "Boss Energy",
    trackInfo: "A fierce hip-hop anthem celebrating confidence, independence, and female empowerment",
    primaryGenreId: "hiphop",
    secondaryGenreId: "pop"
  },
  {
    title: "Dynamite",
    labelName: "K-Pop Global",
    albumName: "International Vibes",
    trackInfo: "An upbeat disco-pop track that brings joy and energy with its retro-inspired sound",
    primaryGenreId: "pop",
    secondaryGenreId: "disco"
  },
  {
    title: "Mood",
    labelName: "Chill Vibes Records",
    albumName: "Laid Back Anthems",
    trackInfo: "A laid-back hip-hop track about enjoying life's simple pleasures and good vibes",
    primaryGenreId: "hiphop",
    secondaryGenreId: "chill"
  },
  {
    title: "Rockstar",
    labelName: "Trap Nation Records",
    albumName: "Street Anthems",
    trackInfo: "A hard-hitting trap anthem about success, fame, and the rockstar lifestyle",
    primaryGenreId: "hiphop",
    secondaryGenreId: "trap"
  },
  {
    title: "Adore You",
    labelName: "Love Songs Records",
    albumName: "Romantic Gestures",
    trackInfo: "A sweet pop ballad about unconditional love and adoration for someone special",
    primaryGenreId: "pop",
    secondaryGenreId: "ballad"
  },
  {
    title: "Intentions",
    labelName: "Pure Love Records",
    albumName: "Honest Hearts",
    trackInfo: "A heartfelt R&B-pop track about genuine love and pure romantic intentions",
    primaryGenreId: "pop",
    secondaryGenreId: "rnb"
  },
  {
    title: "The Box",
    labelName: "Trap House Records",
    albumName: "Street Stories",
    trackInfo: "A minimalist trap hit with a catchy hook and raw street storytelling",
    primaryGenreId: "hiphop",
    secondaryGenreId: "trap"
  },
  {
    title: "Memories",
    labelName: "Nostalgic Pop Records",
    albumName: "Time Machine",
    trackInfo: "An emotional pop-rock anthem about cherishing memories and honoring lost loved ones",
    primaryGenreId: "pop",
    secondaryGenreId: "rock"
  },
  {
    title: "Someone You Loved",
    labelName: "Heartbreak Records",
    albumName: "Emotional Journey",
    trackInfo: "A powerful piano ballad about the pain of losing someone you deeply loved",
    primaryGenreId: "pop",
    secondaryGenreId: "ballad"
  },
  {
    title: "Sunflower",
    labelName: "Feel Good Records",
    albumName: "Positive Vibes",
    trackInfo: "An uplifting hip-hop track with a sunny disposition and feel-good energy",
    primaryGenreId: "hiphop",
    secondaryGenreId: "pop"
  },
  {
    title: "Bad Guy",
    labelName: "Alternative Pop Records",
    albumName: "Dark Pop Anthems",
    trackInfo: "A minimalist dark pop track with a haunting beat and provocative lyrics",
    primaryGenreId: "pop",
    secondaryGenreId: "alternative"
  },
  {
    title: "7 rings",
    labelName: "Luxury Lifestyle Records",
    albumName: "Material Dreams",
    trackInfo: "A trap-influenced pop anthem about success, luxury, and treating yourself",
    primaryGenreId: "pop",
    secondaryGenreId: "trap"
  },
  {
    title: "Old Town Road",
    labelName: "Country Trap Records",
    albumName: "Genre Bending",
    trackInfo: "A genre-defying country-trap fusion about freedom and the open road",
    primaryGenreId: "country",
    secondaryGenreId: "hiphop"
  },
  {
    title: "Truth Hurts",
    labelName: "Self Love Anthems",
    albumName: "Confidence Boost",
    trackInfo: "An empowering anthem about self-worth and moving on from toxic relationships",
    primaryGenreId: "pop",
    secondaryGenreId: "rnb"
  },
  {
    title: "Senorita",
    labelName: "Latin Pop Records",
    albumName: "Tropical Romance",
    trackInfo: "A sultry Latin-influenced pop duet about passionate romance and desire",
    primaryGenreId: "pop",
    secondaryGenreId: "latin"
  },
  {
    title: "Lose You To Love Me",
    labelName: "Healing Hearts Records",
    albumName: "Self Discovery",
    trackInfo: "An emotional ballad about finding yourself after letting go of a toxic relationship",
    primaryGenreId: "pop",
    secondaryGenreId: "ballad"
  },
  {
    title: "Circles",
    labelName: "Indie Rock Revival",
    albumName: "Modern Classics",
    trackInfo: "A dreamy indie-rock track about being caught in endless cycles of love and loss",
    primaryGenreId: "rock",
    secondaryGenreId: "indie"
  },
  {
    title: "Falling",
    labelName: "Vulnerable Voices",
    albumName: "Raw Emotions",
    trackInfo: "A stripped-down acoustic ballad about vulnerability and the fear of falling in love",
    primaryGenreId: "pop",
    secondaryGenreId: "acoustic"
  },
  {
    title: "Roses",
    labelName: "Electronic Fusion",
    albumName: "Digital Dreams",
    trackInfo: "An electronic-pop fusion track with infectious beats and romantic undertones",
    primaryGenreId: "electronic",
    secondaryGenreId: "pop"
  },
  {
    title: "Blueberry Faygo",
    labelName: "Melodic Rap Records",
    albumName: "Sweet Sounds",
    trackInfo: "A melodic rap track with catchy hooks and smooth production about success and lifestyle",
    primaryGenreId: "hiphop",
    secondaryGenreId: "melodic"
  },
  {
    title: "Toosie Slide",
    labelName: "Dance Craze Records",
    albumName: "Viral Moves",
    trackInfo: "A catchy hip-hop track designed for dancing with simple, memorable choreography",
    primaryGenreId: "hiphop",
    secondaryGenreId: "dance"
  },
  {
    title: "Say So",
    labelName: "TikTok Hits Records",
    albumName: "Social Media Anthems",
    trackInfo: "A disco-influenced pop track that became a viral sensation with its catchy dance moves",
    primaryGenreId: "pop",
    secondaryGenreId: "disco"
  },
  {
    title: "The Bones",
    labelName: "Country Soul Records",
    albumName: "Strong Foundations",
    trackInfo: "A country-pop ballad about having a strong foundation in love and relationships",
    primaryGenreId: "country",
    secondaryGenreId: "pop"
  },
  {
    title: "Stupid Love",
    labelName: "Dance Pop Records",
    albumName: "Love Revolution",
    trackInfo: "An energetic dance-pop anthem about the irresistible power of love and attraction",
    primaryGenreId: "pop",
    secondaryGenreId: "dance"
  },
  {
    title: "Rain on Me",
    labelName: "Collaboration Records",
    albumName: "Dance Floor Hits",
    trackInfo: "An uplifting dance-pop collaboration about resilience and dancing through life's storms",
    primaryGenreId: "pop",
    secondaryGenreId: "dance"
  }
];

// 生成完整的音乐数据
function generateEnglishSongsData() {
  const coverUrls = [];
  
  // 生成10个封面URL
  for (let i = 1; i <= 10; i++) {
    coverUrls.push(`https://example.com/cover${i}.jpg`);
  }
  
  // 生成50条音乐数据
  const musicData = englishSongs.map((song, index) => {
    const trackNumber = String(index + 1).padStart(3, '0');
    
    return {
      title: song.title,
      labelName: song.labelName,
      albumName: song.albumName,
      trackInfo: song.trackInfo,
      primaryLanguage: "en",
      upc: `12345678${trackNumber}`,
      isrc: `USRC240${trackNumber}`,
      primaryGenreId: song.primaryGenreId,
      secondaryGenreId: song.secondaryGenreId,
      copyrightName: song.labelName,
      copyrightYear: 2024,
      phonogramCopyright: song.labelName,
      phonogramCopyrightYear: 2024,
      audioFormats: ["mp3", "wav"],
      releaseOptions: ["digital", "streaming"],
      mediaUrls: [`https://example.com/audio/track${trackNumber}.mp3`]
    };
  });
  
  return {
    coverUrls,
    musicData
  };
}

// 主函数
function main() {
  console.log('🎵 生成50首英文歌曲数据...');
  
  const data = generateEnglishSongsData();
  const outputFile = path.join(__dirname, 'music-data-50-tracks.json');
  
  // 写入文件
  fs.writeFileSync(outputFile, JSON.stringify(data, null, 2), 'utf8');
  
  console.log(`✅ 成功生成 ${data.musicData.length} 首英文歌曲数据`);
  console.log(`📄 文件保存到: ${outputFile}`);
  console.log(`🖼️  包含 ${data.coverUrls.length} 个封面URL`);
  
  // 显示前5条数据作为示例
  console.log('\n📋 前5首歌曲示例:');
  data.musicData.slice(0, 5).forEach((track, index) => {
    console.log(`${index + 1}. ${track.title} - ${track.albumName}`);
  });
}

// 运行
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { generateEnglishSongsData };
